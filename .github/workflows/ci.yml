name: CI/CD Pipeline

on:
  push:
    branches:
      - main
      - master
  pull_request:

jobs:
  build:
    runs-on: ubuntu-latest

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up JDK 17
      uses: actions/setup-java@v4
      with:
        java-version: '17'
        distribution: 'temurin'

    - name: Cache Gradle packages
      uses: actions/cache@v4
      with:
        path: |
          ~/.gradle/caches
          ~/.gradle/wrapper
        key: ${{ runner.os }}-gradle-${{ hashFiles('**/*.gradle*', '**/gradle-wrapper.properties') }}
        restore-keys: |
          ${{ runner.os }}-gradle-

    - name: <PERSON> execute permission for gradlew
      run: chmod +x gradlew

    - name: Set up test environment
      run: |
        echo "Setting up test environment..."
        # Create .env file for tests with dummy values
        echo "GOOGLE_CLIENT_ID=test-client-id" > .env
        echo "GOOGLE_CLIENT_SECRET=test-client-secret" >> .env
        echo "AWS_ACCESS_KEY_ID=test-access-key" >> .env
        echo "AWS_SECRET_ACCESS_KEY=test-secret-key" >> .env
        echo "AWS_REGION=us-east-1" >> .env
        echo "S3_BUCKET_NAME=test-bucket" >> .env

    - name: Run tests
      run: |
        echo "Running tests..."
        ./gradlew test --info
      env:
        SPRING_PROFILES_ACTIVE: test

    - name: Build project
      run: |
        echo "Building the project..."
        ./gradlew build -x test
      env:
        SPRING_PROFILES_ACTIVE: test

    - name: Upload artifacts
      uses: actions/upload-artifact@v4
      with:
        name: build-artifacts
        path: build/libs/*.jar
