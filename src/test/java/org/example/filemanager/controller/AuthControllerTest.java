package org.example.filemanager.controller;

import org.example.filemanager.model.User;
import org.example.filemanager.service.UserService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.servlet.MockMvc;

import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;
import static org.springframework.security.test.web.servlet.request.SecurityMockMvcRequestPostProcessors.csrf;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@WebMvcTest(AuthController.class)
@ActiveProfiles("test")
class AuthControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private UserService userService;

    @Test
    void testLocalLogin_Success() throws Exception {
        // Given
        User mockUser = new User("<EMAIL>", "Test User", "encoded-password");
        when(userService.authenticateLocalUser("<EMAIL>", "password123"))
                .thenReturn(mockUser);

        // When & Then
        mockMvc.perform(post("/login/local")
                .param("email", "<EMAIL>")
                .param("password", "password123")
                .with(csrf()))
                .andExpect(status().is3xxRedirection())
                .andExpect(redirectedUrl("/"));
    }

    @Test
    void testLocalLogin_InvalidCredentials() throws Exception {
        // Given
        when(userService.authenticateLocalUser("<EMAIL>", "wrongpassword"))
                .thenReturn(null);

        // When & Then
        mockMvc.perform(post("/login/local")
                .param("email", "<EMAIL>")
                .param("password", "wrongpassword")
                .with(csrf()))
                .andExpect(status().is3xxRedirection())
                .andExpect(redirectedUrl("/login?error=invalid"));
    }

    @Test
    void testRegister_Success() throws Exception {
        // Given
        User mockUser = new User("<EMAIL>", "Test User", "encoded-password");
        when(userService.userExists("<EMAIL>")).thenReturn(false);
        when(userService.registerLocalUser("<EMAIL>", "Test User", "password123"))
                .thenReturn(mockUser);

        // When & Then
        mockMvc.perform(post("/register")
                .param("email", "<EMAIL>")
                .param("name", "Test User")
                .param("password", "password123")
                .param("confirmPassword", "password123")
                .with(csrf()))
                .andExpect(status().is3xxRedirection())
                .andExpect(redirectedUrl("/?registered=true"));
    }

    @Test
    void testRegister_UserAlreadyExists() throws Exception {
        // Given
        when(userService.userExists("<EMAIL>")).thenReturn(true);

        // When & Then
        mockMvc.perform(post("/register")
                .param("email", "<EMAIL>")
                .param("name", "Test User")
                .param("password", "password123")
                .param("confirmPassword", "password123")
                .with(csrf()))
                .andExpect(status().is3xxRedirection())
                .andExpect(redirectedUrl("/login?error=user_exists"));
    }

    @Test
    void testRegister_PasswordMismatch() throws Exception {
        // When & Then
        mockMvc.perform(post("/register")
                .param("email", "<EMAIL>")
                .param("name", "Test User")
                .param("password", "password123")
                .param("confirmPassword", "differentpassword")
                .with(csrf()))
                .andExpect(status().is3xxRedirection())
                .andExpect(redirectedUrl("/login?error=password_mismatch"));
    }

    @Test
    void testRegister_WeakPassword() throws Exception {
        // When & Then
        mockMvc.perform(post("/register")
                .param("email", "<EMAIL>")
                .param("name", "Test User")
                .param("password", "123")
                .param("confirmPassword", "123")
                .with(csrf()))
                .andExpect(status().is3xxRedirection())
                .andExpect(redirectedUrl("/login?error=password_weak"));
    }

    @Test
    void testRegister_EmptyEmail() throws Exception {
        // When & Then
        mockMvc.perform(post("/register")
                .param("email", "")
                .param("name", "Test User")
                .param("password", "password123")
                .param("confirmPassword", "password123")
                .with(csrf()))
                .andExpect(status().is3xxRedirection())
                .andExpect(redirectedUrl("/login?error=email_required"));
    }

    @Test
    void testRegister_EmptyName() throws Exception {
        // When & Then
        mockMvc.perform(post("/register")
                .param("email", "<EMAIL>")
                .param("name", "")
                .param("password", "password123")
                .param("confirmPassword", "password123")
                .with(csrf()))
                .andExpect(status().is3xxRedirection())
                .andExpect(redirectedUrl("/login?error=name_required"));
    }
}
