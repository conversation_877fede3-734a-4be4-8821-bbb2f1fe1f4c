# Test configuration for CI/CD
# Disable OAuth2 for tests
spring.security.oauth2.client.registration.google.client-id=test-client-id
spring.security.oauth2.client.registration.google.client-secret=test-client-secret

# AWS Configuration for tests (mock values)
aws.region=us-east-1
aws.s3.bucket-name=test-bucket
aws.access-key-id=test-access-key
aws.secret-access-key=test-secret-key

# Disable AWS auto-configuration for tests
spring.cloud.aws.credentials.access-key=test-access-key
spring.cloud.aws.credentials.secret-key=test-secret-key
spring.cloud.aws.region.static=us-east-1
spring.cloud.aws.stack.auto=false

# Logging for tests
logging.level.org.example=INFO
logging.level.org.springframework.security=WARN
logging.level.software.amazon.awssdk=WARN

# Allow circular references as fallback
spring.main.allow-circular-references=true
